{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/index.ts"], "sourcesContent": ["/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nimport CssSyntaxError from './CssSyntaxError'\nimport Warning from '../../postcss-loader/src/Warning'\nimport { stringifyRequest } from '../../../stringify-request'\n\nconst moduleRegExp = /\\.module\\.\\w+$/i\n\nfunction getModulesOptions(rawOptions: any, loaderContext: any) {\n  const { resourcePath } = loaderContext\n\n  if (typeof rawOptions.modules === 'undefined') {\n    const isModules = moduleRegExp.test(resourcePath)\n\n    if (!isModules) {\n      return false\n    }\n  } else if (\n    typeof rawOptions.modules === 'boolean' &&\n    rawOptions.modules === false\n  ) {\n    return false\n  }\n\n  let modulesOptions: any = {\n    compileType: rawOptions.icss ? 'icss' : 'module',\n    auto: true,\n    mode: 'local',\n    exportGlobals: false,\n    localIdentName: '[hash:base64]',\n    localIdentContext: loaderContext.rootContext,\n    localIdentHashPrefix: '',\n    // eslint-disable-next-line no-undefined\n    localIdentRegExp: undefined,\n    namedExport: false,\n    exportLocalsConvention: 'asIs',\n    exportOnlyLocals: false,\n  }\n\n  if (\n    typeof rawOptions.modules === 'boolean' ||\n    typeof rawOptions.modules === 'string'\n  ) {\n    modulesOptions.mode =\n      typeof rawOptions.modules === 'string' ? rawOptions.modules : 'local'\n  } else {\n    if (rawOptions.modules) {\n      if (typeof rawOptions.modules.auto === 'boolean') {\n        const isModules =\n          rawOptions.modules.auto && moduleRegExp.test(resourcePath)\n\n        if (!isModules) {\n          return false\n        }\n      } else if (rawOptions.modules.auto instanceof RegExp) {\n        const isModules = rawOptions.modules.auto.test(resourcePath)\n\n        if (!isModules) {\n          return false\n        }\n      } else if (typeof rawOptions.modules.auto === 'function') {\n        const isModule = rawOptions.modules.auto(resourcePath)\n\n        if (!isModule) {\n          return false\n        }\n      }\n\n      if (\n        rawOptions.modules.namedExport === true &&\n        typeof rawOptions.modules.exportLocalsConvention === 'undefined'\n      ) {\n        modulesOptions.exportLocalsConvention = 'camelCaseOnly'\n      }\n    }\n\n    modulesOptions = { ...modulesOptions, ...(rawOptions.modules || {}) }\n  }\n\n  if (typeof modulesOptions.mode === 'function') {\n    modulesOptions.mode = modulesOptions.mode(loaderContext.resourcePath)\n  }\n\n  if (modulesOptions.namedExport === true) {\n    if (rawOptions.esModule === false) {\n      throw new Error(\n        'The \"modules.namedExport\" option requires the \"esModules\" option to be enabled'\n      )\n    }\n\n    if (modulesOptions.exportLocalsConvention !== 'camelCaseOnly') {\n      throw new Error(\n        'The \"modules.namedExport\" option requires the \"modules.exportLocalsConvention\" option to be \"camelCaseOnly\"'\n      )\n    }\n  }\n\n  return modulesOptions\n}\n\nfunction normalizeOptions(rawOptions: any, loaderContext: any) {\n  if (rawOptions.icss) {\n    loaderContext.emitWarning(\n      new Error(\n        'The \"icss\" option is deprecated, use \"modules.compileType: \"icss\"\" instead'\n      )\n    )\n  }\n\n  const modulesOptions = getModulesOptions(rawOptions, loaderContext)\n\n  return {\n    url: typeof rawOptions.url === 'undefined' ? true : rawOptions.url,\n    import: typeof rawOptions.import === 'undefined' ? true : rawOptions.import,\n    modules: modulesOptions,\n    // TODO remove in the next major release\n    icss: typeof rawOptions.icss === 'undefined' ? false : rawOptions.icss,\n    sourceMap:\n      typeof rawOptions.sourceMap === 'boolean'\n        ? rawOptions.sourceMap\n        : loaderContext.sourceMap,\n    importLoaders:\n      typeof rawOptions.importLoaders === 'string'\n        ? parseInt(rawOptions.importLoaders, 10)\n        : rawOptions.importLoaders,\n    esModule:\n      typeof rawOptions.esModule === 'undefined' ? true : rawOptions.esModule,\n    fontLoader: rawOptions.fontLoader,\n  }\n}\n\nexport default async function loader(\n  this: any,\n  content: string,\n  map: any,\n  meta: any\n) {\n  const rawOptions = this.getOptions()\n\n  const plugins: any[] = []\n  const callback = this.async()\n\n  const loaderSpan = this.currentTraceSpan.traceChild('css-loader')\n\n  loaderSpan\n    .traceAsyncFn(async () => {\n      let options: any\n\n      try {\n        options = normalizeOptions(rawOptions, this)\n      } catch (error) {\n        throw error\n      }\n\n      const { postcss } = await rawOptions.postcss()\n\n      const {\n        shouldUseModulesPlugins,\n        shouldUseImportPlugin,\n        shouldUseURLPlugin,\n        shouldUseIcssPlugin,\n        getPreRequester,\n        getExportCode,\n        getFilter,\n        getImportCode,\n        getModuleCode,\n        getModulesPlugins,\n        normalizeSourceMap,\n        sort,\n      } = require('./utils')\n\n      const { icssParser, importParser, urlParser } = require('./plugins')\n\n      const replacements: any[] = []\n      // if it's a font loader next-font-loader will have exports that should be exported as is\n      const exports = options.fontLoader ? meta.exports : []\n\n      if (shouldUseModulesPlugins(options)) {\n        plugins.push(...getModulesPlugins(options, this, meta))\n      }\n\n      const importPluginImports: any[] = []\n      const importPluginApi: any[] = []\n\n      if (shouldUseImportPlugin(options)) {\n        const resolver = this.getResolve({\n          conditionNames: ['style'],\n          extensions: ['.css'],\n          mainFields: ['css', 'style', 'main', '...'],\n          mainFiles: ['index', '...'],\n          restrictions: [/\\.css$/i],\n        })\n\n        plugins.push(\n          importParser({\n            imports: importPluginImports,\n            api: importPluginApi,\n            context: this.context,\n            rootContext: this.rootContext,\n            filter: getFilter(options.import, this.resourcePath),\n            resolver,\n            urlHandler: (url: any) =>\n              stringifyRequest(\n                this,\n                getPreRequester(this)(options.importLoaders) + url\n              ),\n          })\n        )\n      }\n\n      const urlPluginImports: any[] = []\n\n      if (shouldUseURLPlugin(options)) {\n        const urlResolver = this.getResolve({\n          conditionNames: ['asset'],\n          mainFields: ['asset'],\n          mainFiles: [],\n          extensions: [],\n        })\n\n        plugins.push(\n          urlParser({\n            imports: urlPluginImports,\n            replacements,\n            context: this.context,\n            rootContext: this.rootContext,\n            filter: getFilter(options.url, this.resourcePath),\n            resolver: urlResolver,\n            urlHandler: (url: string) => stringifyRequest(this, url),\n          })\n        )\n      }\n\n      const icssPluginImports: any[] = []\n      const icssPluginApi: any[] = []\n\n      if (shouldUseIcssPlugin(options)) {\n        const icssResolver = this.getResolve({\n          conditionNames: ['style'],\n          extensions: [],\n          mainFields: ['css', 'style', 'main', '...'],\n          mainFiles: ['index', '...'],\n        })\n\n        plugins.push(\n          icssParser({\n            imports: icssPluginImports,\n            api: icssPluginApi,\n            replacements,\n            exports,\n            context: this.context,\n            rootContext: this.rootContext,\n            resolver: icssResolver,\n            urlHandler: (url: string) =>\n              stringifyRequest(\n                this,\n                getPreRequester(this)(options.importLoaders) + url\n              ),\n          })\n        )\n      }\n\n      // Reuse CSS AST (PostCSS AST e.g 'postcss-loader') to avoid reparsing\n      if (meta) {\n        const { ast } = meta\n\n        if (ast && ast.type === 'postcss') {\n          // eslint-disable-next-line no-param-reassign\n          content = ast.root\n          loaderSpan.setAttribute('astUsed', 'true')\n        }\n      }\n\n      const { resourcePath } = this\n\n      let result\n\n      try {\n        result = await postcss(plugins).process(content, {\n          from: resourcePath,\n          to: resourcePath,\n          map: options.sourceMap\n            ? {\n                prev: map ? normalizeSourceMap(map, resourcePath) : null,\n                inline: false,\n                annotation: false,\n              }\n            : false,\n        })\n      } catch (error: any) {\n        if (error.file) {\n          this.addDependency(error.file)\n        }\n\n        throw error.name === 'CssSyntaxError'\n          ? new CssSyntaxError(error)\n          : error\n      }\n\n      for (const warning of result.warnings()) {\n        this.emitWarning(new Warning(warning))\n      }\n\n      const imports = [\n        ...icssPluginImports.sort(sort),\n        ...importPluginImports.sort(sort),\n        ...urlPluginImports.sort(sort),\n      ]\n\n      const api = [...importPluginApi.sort(sort), ...icssPluginApi.sort(sort)]\n\n      if (options.modules.exportOnlyLocals !== true) {\n        imports.unshift({\n          importName: '___CSS_LOADER_API_IMPORT___',\n          url: stringifyRequest(this, require.resolve('./runtime/api')),\n        })\n      }\n\n      const importCode = getImportCode(imports, options)\n      const moduleCode = getModuleCode(result, api, replacements, options, this)\n      const exportCode = getExportCode(exports, replacements, options)\n\n      return `${importCode}${moduleCode}${exportCode}`\n    })\n    .then(\n      (code: string) => {\n        callback(null, code)\n      },\n      (err: Error) => {\n        callback(err)\n      }\n    )\n}\n"], "names": ["loader", "moduleRegExp", "getModulesOptions", "rawOptions", "loaderContext", "resourcePath", "modules", "isModules", "test", "modulesOptions", "compileType", "icss", "auto", "mode", "exportGlobals", "localIdentName", "localIdentContext", "rootContext", "localIdentHashPrefix", "localIdentRegExp", "undefined", "namedExport", "exportLocalsConvention", "exportOnlyLocals", "RegExp", "isModule", "esModule", "Error", "normalizeOptions", "emitWarning", "url", "import", "sourceMap", "importLoaders", "parseInt", "fontLoader", "content", "map", "meta", "getOptions", "plugins", "callback", "async", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "error", "postcss", "shouldUseModulesPlugins", "shouldUseImportPlugin", "shouldUseURLPlugin", "shouldUseIcssPlugin", "getPreRequester", "getExportCode", "getFilter", "getImportCode", "getModuleCode", "getModulesPlugins", "normalizeSourceMap", "sort", "require", "icss<PERSON><PERSON><PERSON>", "importParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replacements", "exports", "push", "importPluginImports", "importPluginApi", "resolver", "getResolve", "conditionNames", "extensions", "mainFields", "mainFiles", "restrictions", "imports", "api", "context", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stringifyRequest", "urlPluginImports", "urlResolver", "icssPluginImports", "icssPluginApi", "icssResolver", "ast", "type", "root", "setAttribute", "result", "process", "from", "to", "prev", "inline", "annotation", "file", "addDependency", "name", "CssSyntaxError", "warning", "warnings", "Warning", "unshift", "importName", "resolve", "importCode", "moduleCode", "exportCode", "then", "code", "err"], "mappings": "AAAA;;;AAGA;;;;+BAkIA;;;eAA8BA;;;uEAjIH;gEACP;kCACa;;;;;;AAEjC,MAAMC,eAAe;AAErB,SAASC,kBAAkBC,UAAe,EAAEC,aAAkB;IAC5D,MAAM,EAAEC,YAAY,EAAE,GAAGD;IAEzB,IAAI,OAAOD,WAAWG,OAAO,KAAK,aAAa;QAC7C,MAAMC,YAAYN,aAAaO,IAAI,CAACH;QAEpC,IAAI,CAACE,WAAW;YACd,OAAO;QACT;IACF,OAAO,IACL,OAAOJ,WAAWG,OAAO,KAAK,aAC9BH,WAAWG,OAAO,KAAK,OACvB;QACA,OAAO;IACT;IAEA,IAAIG,iBAAsB;QACxBC,aAAaP,WAAWQ,IAAI,GAAG,SAAS;QACxCC,MAAM;QACNC,MAAM;QACNC,eAAe;QACfC,gBAAgB;QAChBC,mBAAmBZ,cAAca,WAAW;QAC5CC,sBAAsB;QACtB,wCAAwC;QACxCC,kBAAkBC;QAClBC,aAAa;QACbC,wBAAwB;QACxBC,kBAAkB;IACpB;IAEA,IACE,OAAOpB,WAAWG,OAAO,KAAK,aAC9B,OAAOH,WAAWG,OAAO,KAAK,UAC9B;QACAG,eAAeI,IAAI,GACjB,OAAOV,WAAWG,OAAO,KAAK,WAAWH,WAAWG,OAAO,GAAG;IAClE,OAAO;QACL,IAAIH,WAAWG,OAAO,EAAE;YACtB,IAAI,OAAOH,WAAWG,OAAO,CAACM,IAAI,KAAK,WAAW;gBAChD,MAAML,YACJJ,WAAWG,OAAO,CAACM,IAAI,IAAIX,aAAaO,IAAI,CAACH;gBAE/C,IAAI,CAACE,WAAW;oBACd,OAAO;gBACT;YACF,OAAO,IAAIJ,WAAWG,OAAO,CAACM,IAAI,YAAYY,QAAQ;gBACpD,MAAMjB,YAAYJ,WAAWG,OAAO,CAACM,IAAI,CAACJ,IAAI,CAACH;gBAE/C,IAAI,CAACE,WAAW;oBACd,OAAO;gBACT;YACF,OAAO,IAAI,OAAOJ,WAAWG,OAAO,CAACM,IAAI,KAAK,YAAY;gBACxD,MAAMa,WAAWtB,WAAWG,OAAO,CAACM,IAAI,CAACP;gBAEzC,IAAI,CAACoB,UAAU;oBACb,OAAO;gBACT;YACF;YAEA,IACEtB,WAAWG,OAAO,CAACe,WAAW,KAAK,QACnC,OAAOlB,WAAWG,OAAO,CAACgB,sBAAsB,KAAK,aACrD;gBACAb,eAAea,sBAAsB,GAAG;YAC1C;QACF;QAEAb,iBAAiB;YAAE,GAAGA,cAAc;YAAE,GAAIN,WAAWG,OAAO,IAAI,CAAC,CAAC;QAAE;IACtE;IAEA,IAAI,OAAOG,eAAeI,IAAI,KAAK,YAAY;QAC7CJ,eAAeI,IAAI,GAAGJ,eAAeI,IAAI,CAACT,cAAcC,YAAY;IACtE;IAEA,IAAII,eAAeY,WAAW,KAAK,MAAM;QACvC,IAAIlB,WAAWuB,QAAQ,KAAK,OAAO;YACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,mFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIlB,eAAea,sBAAsB,KAAK,iBAAiB;YAC7D,MAAM,qBAEL,CAFK,IAAIK,MACR,gHADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,OAAOlB;AACT;AAEA,SAASmB,iBAAiBzB,UAAe,EAAEC,aAAkB;IAC3D,IAAID,WAAWQ,IAAI,EAAE;QACnBP,cAAcyB,WAAW,CACvB,qBAEC,CAFD,IAAIF,MACF,+EADF,qBAAA;mBAAA;wBAAA;0BAAA;QAEA;IAEJ;IAEA,MAAMlB,iBAAiBP,kBAAkBC,YAAYC;IAErD,OAAO;QACL0B,KAAK,OAAO3B,WAAW2B,GAAG,KAAK,cAAc,OAAO3B,WAAW2B,GAAG;QAClEC,QAAQ,OAAO5B,WAAW4B,MAAM,KAAK,cAAc,OAAO5B,WAAW4B,MAAM;QAC3EzB,SAASG;QACT,wCAAwC;QACxCE,MAAM,OAAOR,WAAWQ,IAAI,KAAK,cAAc,QAAQR,WAAWQ,IAAI;QACtEqB,WACE,OAAO7B,WAAW6B,SAAS,KAAK,YAC5B7B,WAAW6B,SAAS,GACpB5B,cAAc4B,SAAS;QAC7BC,eACE,OAAO9B,WAAW8B,aAAa,KAAK,WAChCC,SAAS/B,WAAW8B,aAAa,EAAE,MACnC9B,WAAW8B,aAAa;QAC9BP,UACE,OAAOvB,WAAWuB,QAAQ,KAAK,cAAc,OAAOvB,WAAWuB,QAAQ;QACzES,YAAYhC,WAAWgC,UAAU;IACnC;AACF;AAEe,eAAenC,OAE5BoC,OAAe,EACfC,GAAQ,EACRC,IAAS;IAET,MAAMnC,aAAa,IAAI,CAACoC,UAAU;IAElC,MAAMC,UAAiB,EAAE;IACzB,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAEpDF,WACGG,YAAY,CAAC;QACZ,IAAIC;QAEJ,IAAI;YACFA,UAAUnB,iBAAiBzB,YAAY,IAAI;QAC7C,EAAE,OAAO6C,OAAO;YACd,MAAMA;QACR;QAEA,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAM9C,WAAW8C,OAAO;QAE5C,MAAM,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,kBAAkB,EAClBC,IAAI,EACL,GAAGC,QAAQ;QAEZ,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAE,GAAGH,QAAQ;QAExD,MAAMI,eAAsB,EAAE;QAC9B,yFAAyF;QACzF,MAAMC,WAAUpB,QAAQZ,UAAU,GAAGG,KAAK6B,OAAO,GAAG,EAAE;QAEtD,IAAIjB,wBAAwBH,UAAU;YACpCP,QAAQ4B,IAAI,IAAIT,kBAAkBZ,SAAS,IAAI,EAAET;QACnD;QAEA,MAAM+B,sBAA6B,EAAE;QACrC,MAAMC,kBAAyB,EAAE;QAEjC,IAAInB,sBAAsBJ,UAAU;YAClC,MAAMwB,WAAW,IAAI,CAACC,UAAU,CAAC;gBAC/BC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAO;gBACpBC,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3BC,cAAc;oBAAC;iBAAU;YAC3B;YAEArC,QAAQ4B,IAAI,CACVJ,aAAa;gBACXc,SAAST;gBACTU,KAAKT;gBACLU,SAAS,IAAI,CAACA,OAAO;gBACrB/D,aAAa,IAAI,CAACA,WAAW;gBAC7BgE,QAAQzB,UAAUT,QAAQhB,MAAM,EAAE,IAAI,CAAC1B,YAAY;gBACnDkE;gBACAW,YAAY,CAACpD,MACXqD,IAAAA,kCAAgB,EACd,IAAI,EACJ7B,gBAAgB,IAAI,EAAEP,QAAQd,aAAa,IAAIH;YAErD;QAEJ;QAEA,MAAMsD,mBAA0B,EAAE;QAElC,IAAIhC,mBAAmBL,UAAU;YAC/B,MAAMsC,cAAc,IAAI,CAACb,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBE,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbF,YAAY,EAAE;YAChB;YAEAlC,QAAQ4B,IAAI,CACVH,UAAU;gBACRa,SAASM;gBACTlB;gBACAc,SAAS,IAAI,CAACA,OAAO;gBACrB/D,aAAa,IAAI,CAACA,WAAW;gBAC7BgE,QAAQzB,UAAUT,QAAQjB,GAAG,EAAE,IAAI,CAACzB,YAAY;gBAChDkE,UAAUc;gBACVH,YAAY,CAACpD,MAAgBqD,IAAAA,kCAAgB,EAAC,IAAI,EAAErD;YACtD;QAEJ;QAEA,MAAMwD,oBAA2B,EAAE;QACnC,MAAMC,gBAAuB,EAAE;QAE/B,IAAIlC,oBAAoBN,UAAU;YAChC,MAAMyC,eAAe,IAAI,CAAChB,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY,EAAE;gBACdC,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEApC,QAAQ4B,IAAI,CACVL,WAAW;gBACTe,SAASQ;gBACTP,KAAKQ;gBACLrB;gBACAC,SAAAA;gBACAa,SAAS,IAAI,CAACA,OAAO;gBACrB/D,aAAa,IAAI,CAACA,WAAW;gBAC7BsD,UAAUiB;gBACVN,YAAY,CAACpD,MACXqD,IAAAA,kCAAgB,EACd,IAAI,EACJ7B,gBAAgB,IAAI,EAAEP,QAAQd,aAAa,IAAIH;YAErD;QAEJ;QAEA,sEAAsE;QACtE,IAAIQ,MAAM;YACR,MAAM,EAAEmD,GAAG,EAAE,GAAGnD;YAEhB,IAAImD,OAAOA,IAAIC,IAAI,KAAK,WAAW;gBACjC,6CAA6C;gBAC7CtD,UAAUqD,IAAIE,IAAI;gBAClBhD,WAAWiD,YAAY,CAAC,WAAW;YACrC;QACF;QAEA,MAAM,EAAEvF,YAAY,EAAE,GAAG,IAAI;QAE7B,IAAIwF;QAEJ,IAAI;YACFA,SAAS,MAAM5C,QAAQT,SAASsD,OAAO,CAAC1D,SAAS;gBAC/C2D,MAAM1F;gBACN2F,IAAI3F;gBACJgC,KAAKU,QAAQf,SAAS,GAClB;oBACEiE,MAAM5D,MAAMuB,mBAAmBvB,KAAKhC,gBAAgB;oBACpD6F,QAAQ;oBACRC,YAAY;gBACd,IACA;YACN;QACF,EAAE,OAAOnD,OAAY;YACnB,IAAIA,MAAMoD,IAAI,EAAE;gBACd,IAAI,CAACC,aAAa,CAACrD,MAAMoD,IAAI;YAC/B;YAEA,MAAMpD,MAAMsD,IAAI,KAAK,mBACjB,qBAAyB,CAAzB,IAAIC,uBAAc,CAACvD,QAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAwB,KACxBA;QACN;QAEA,KAAK,MAAMwD,WAAWX,OAAOY,QAAQ,GAAI;YACvC,IAAI,CAAC5E,WAAW,CAAC,qBAAoB,CAApB,IAAI6E,gBAAO,CAACF,UAAZ,qBAAA;uBAAA;4BAAA;8BAAA;YAAmB;QACtC;QAEA,MAAM1B,UAAU;eACXQ,kBAAkBzB,IAAI,CAACA;eACvBQ,oBAAoBR,IAAI,CAACA;eACzBuB,iBAAiBvB,IAAI,CAACA;SAC1B;QAED,MAAMkB,MAAM;eAAIT,gBAAgBT,IAAI,CAACA;eAAU0B,cAAc1B,IAAI,CAACA;SAAM;QAExE,IAAId,QAAQzC,OAAO,CAACiB,gBAAgB,KAAK,MAAM;YAC7CuD,QAAQ6B,OAAO,CAAC;gBACdC,YAAY;gBACZ9E,KAAKqD,IAAAA,kCAAgB,EAAC,IAAI,EAAErB,QAAQ+C,OAAO,CAAC;YAC9C;QACF;QAEA,MAAMC,aAAarD,cAAcqB,SAAS/B;QAC1C,MAAMgE,aAAarD,cAAcmC,QAAQd,KAAKb,cAAcnB,SAAS,IAAI;QACzE,MAAMiE,aAAazD,cAAcY,UAASD,cAAcnB;QAExD,OAAO,GAAG+D,aAAaC,aAAaC,YAAY;IAClD,GACCC,IAAI,CACH,CAACC;QACCzE,SAAS,MAAMyE;IACjB,GACA,CAACC;QACC1E,SAAS0E;IACX;AAEN"}