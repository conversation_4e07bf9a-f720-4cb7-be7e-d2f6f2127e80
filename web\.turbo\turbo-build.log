[?9001h[?1004h[?25l[2J[m[H]0;C:\WINDOWS\system32\cmd.exe [?25h[?25l
> @excelairate/web@0.1.0 build C:\Users\<USER>\ExcelAiRate\web
> next build[5;1H[?25h   [38;2;173;127;168m[1m▲ Next.js 15.3.5
[m
 [37m[1m [m Creating an optimized production build ...
 [32m[1m✓[m Compiled successfully in 8.0s
 [37m[1m [m Linting and checking validity of types  [36m.[?25l[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[79C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[79C
 [32m[1m✓[m Linting and checking validity of types    
 [37m[1m [m Collecting page data  [36m.[m
[K[79C
 [37m[1m [m Collecting page data  [36m..[m
[K[79C
 [37m[1m [m Collecting page data  [36m...[m
[K[79C
 [37m[1m [m Collecting page data  [36m.[m
[K[79C
 [37m[1m [m Collecting page data  [36m..[m
[K[79C
 [37m[1m [m Collecting page data  [36m...[m
[K[79C
 [37m[1m [m Collecting page data  [36m.[m
[K[79C
 [37m[1m [m Collecting page data  [36m..[m
[K[79C
 [37m[1m [m Collecting page data  [36m...[m
[K[79C
 [37m[1m [m Collecting page data  [36m.[m
[K[79C
 [37m[1m [m Collecting page data  [36m..[m
[K[79C
 [32m[1m✓[m Collecting page data    
[?25h[?25l [37m[1m [m Generating static pages (0/5)  [36m[    ][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[=   ][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[==  ][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[=== ][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[ ===][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[  ==][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[   =][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[    ][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[   =][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[  ==][m
[K[79C
 [37m[1m [m Generating static pages (0/5)  [36m[ ===][m
[K[79C
 [32m[1m✓[m Generating static pages (5/5)
[?25h[?25l [37m[1m [m Finalizing page optimization  [36m. [37m[1m [m Collecting build traces  [36m.[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m.[K[50C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m..[K[49C[m
[K[79C
 [37m[1m [m Collecting build traces  [36m...[K[48C[m
[K[79C
 [32m[1m✓[m Collecting build traces    
 [32m[1m✓[m Finalizing page optimization[K[4m[15;1HRoute (app)[24m[33X[4m[33CSize[24m  [4mFirst Load JS[24m    
┌ ○ /                                    5.63 kB[9X[37m[1m[9C107 kB[m
└ ○ /_not-found                            974 B[9X[37m[1m[9C102 kB[m
+ First Load JS shared by all[13X[37m[1m[13C101 kB[m
  ├ chunks/264-37359b4c82403fcc.js       46.3 kB
  ├ chunks/f0abcf2b-2b42abaf2851e5bb.js  53.2 kB
  └ other shared chunks (total)          1.96 kB
[?25h


○  (Static)  prerendered as static content

[?9001l[?1004l
