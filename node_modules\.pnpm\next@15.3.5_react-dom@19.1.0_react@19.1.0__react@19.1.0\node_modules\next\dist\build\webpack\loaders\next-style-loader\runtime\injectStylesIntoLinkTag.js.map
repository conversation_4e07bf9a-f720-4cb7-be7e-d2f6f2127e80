{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoLinkTag.ts"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nconst getTarget = (() => {\n  const memo: any = {}\n\n  return function memorize(target: any) {\n    if (typeof memo[target] === 'undefined') {\n      let styleTarget = document.querySelector(target)\n\n      // Special case to return head of iframe instead of iframe itself\n      if (\n        window.HTMLIFrameElement &&\n        styleTarget instanceof window.HTMLIFrameElement\n      ) {\n        try {\n          // This will throw an exception if access to iframe is blocked\n          // due to cross-origin restrictions\n          styleTarget = (styleTarget as any).contentDocument.head\n        } catch (e) {\n          // istanbul ignore next\n          styleTarget = null\n        }\n      }\n\n      memo[target] = styleTarget\n    }\n\n    return memo[target]\n  }\n})()\n\nmodule.exports = (url: any, options: any) => {\n  options = options || {}\n  options.attributes =\n    typeof options.attributes === 'object' ? options.attributes : {}\n\n  if (typeof options.attributes.nonce === 'undefined') {\n    const nonce =\n      // eslint-disable-next-line no-undef\n      typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null\n\n    if (nonce) {\n      options.attributes.nonce = nonce\n    }\n  }\n\n  const link = document.createElement('link')\n\n  link.rel = 'stylesheet'\n  link.href = url\n\n  Object.keys(options.attributes).forEach((key) => {\n    link.setAttribute(key, options.attributes[key])\n  })\n\n  if (typeof options.insert === 'function') {\n    options.insert(link)\n  } else {\n    const target = getTarget(options.insert || 'head')\n\n    if (!target) {\n      throw new Error(\n        \"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\"\n      )\n    }\n\n    target.appendChild(link)\n  }\n\n  return (newUrl: any) => {\n    if (typeof newUrl === 'string') {\n      link.href = newUrl\n    } else {\n      link.parentNode!.removeChild(link)\n    }\n  }\n}\n"], "names": ["get<PERSON><PERSON><PERSON>", "memo", "memorize", "target", "styleTarget", "document", "querySelector", "window", "HTMLIFrameElement", "contentDocument", "head", "e", "module", "exports", "url", "options", "attributes", "nonce", "__webpack_nonce__", "link", "createElement", "rel", "href", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "insert", "Error", "append<PERSON><PERSON><PERSON>", "newUrl", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,6CAA6C;;AAE7C,MAAMA,YAAY,AAAC,CAAA;IACjB,MAAMC,OAAY,CAAC;IAEnB,OAAO,SAASC,SAASC,MAAW;QAClC,IAAI,OAAOF,IAAI,CAACE,OAAO,KAAK,aAAa;YACvC,IAAIC,cAAcC,SAASC,aAAa,CAACH;YAEzC,iEAAiE;YACjE,IACEI,OAAOC,iBAAiB,IACxBJ,uBAAuBG,OAAOC,iBAAiB,EAC/C;gBACA,IAAI;oBACF,8DAA8D;oBAC9D,mCAAmC;oBACnCJ,cAAc,AAACA,YAAoBK,eAAe,CAACC,IAAI;gBACzD,EAAE,OAAOC,GAAG;oBACV,uBAAuB;oBACvBP,cAAc;gBAChB;YACF;YAEAH,IAAI,CAACE,OAAO,GAAGC;QACjB;QAEA,OAAOH,IAAI,CAACE,OAAO;IACrB;AACF,CAAA;AAEAS,OAAOC,OAAO,GAAG,CAACC,KAAUC;IAC1BA,UAAUA,WAAW,CAAC;IACtBA,QAAQC,UAAU,GAChB,OAAOD,QAAQC,UAAU,KAAK,WAAWD,QAAQC,UAAU,GAAG,CAAC;IAEjE,IAAI,OAAOD,QAAQC,UAAU,CAACC,KAAK,KAAK,aAAa;QACnD,MAAMA,QACJ,oCAAoC;QACpC,OAAOC,sBAAsB,cAAcA,oBAAoB;QAEjE,IAAID,OAAO;YACTF,QAAQC,UAAU,CAACC,KAAK,GAAGA;QAC7B;IACF;IAEA,MAAME,OAAOd,SAASe,aAAa,CAAC;IAEpCD,KAAKE,GAAG,GAAG;IACXF,KAAKG,IAAI,GAAGR;IAEZS,OAAOC,IAAI,CAACT,QAAQC,UAAU,EAAES,OAAO,CAAC,CAACC;QACvCP,KAAKQ,YAAY,CAACD,KAAKX,QAAQC,UAAU,CAACU,IAAI;IAChD;IAEA,IAAI,OAAOX,QAAQa,MAAM,KAAK,YAAY;QACxCb,QAAQa,MAAM,CAACT;IACjB,OAAO;QACL,MAAMhB,SAASH,UAAUe,QAAQa,MAAM,IAAI;QAE3C,IAAI,CAACzB,QAAQ;YACX,MAAM,qBAEL,CAFK,IAAI0B,MACR,4GADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA1B,OAAO2B,WAAW,CAACX;IACrB;IAEA,OAAO,CAACY;QACN,IAAI,OAAOA,WAAW,UAAU;YAC9BZ,KAAKG,IAAI,GAAGS;QACd,OAAO;YACLZ,KAAKa,UAAU,CAAEC,WAAW,CAACd;QAC/B;IACF;AACF"}