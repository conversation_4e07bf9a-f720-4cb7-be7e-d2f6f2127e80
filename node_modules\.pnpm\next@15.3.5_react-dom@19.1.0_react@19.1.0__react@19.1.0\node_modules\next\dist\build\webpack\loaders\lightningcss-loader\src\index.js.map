{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/index.ts"], "sourcesContent": ["import { LightningCssLoader } from './loader'\n\nexport { LightningCssMinifyPlugin } from './minify'\nexport default LightningCssLoader\n"], "names": ["LightningCssMinifyPlugin", "Lightning<PERSON>s<PERSON><PERSON>der"], "mappings": ";;;;;;;;;;;;;;;IAESA,wBAAwB;eAAxBA,gCAAwB;;IACjC,OAAiC;eAAjC;;;wBAHmC;wBAEM;MACzC,WAAeC,0BAAkB"}