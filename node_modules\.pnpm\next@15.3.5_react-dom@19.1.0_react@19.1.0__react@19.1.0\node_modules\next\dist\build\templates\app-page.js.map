{"version": 3, "sources": ["../../../src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["GlobalError", "__next_app__", "pages", "routeModule", "tree", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "AppPageRouteModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;;;;;;;;;;;;;IAoBoBA,WAAW;eAAXA,gCAAW;;IASlBC,YAAY;eAAZA;;IAXEC,KAAK;eAALA;;IAmBFC,WAAW;eAAXA;;IAnBJC,IAAI;eAAJA;;;;gCAjB0B;2BACT;gFAkBa;qBAczB;;;;;;;;;;;;;;;;;;;AALP,MAAMH,eAAe;IAC1BI,SAASC;IACTC,WAAWC;AACb;AAKO,MAAML,cAAc,IAAIM,kCAAkB,CAAC;IAChDC,YAAY;QACVC,MAAMC,oBAAS,CAACC,QAAQ;QACxBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;QACVC,UAAU,EAAE;IACd;IACAC,UAAU;QACRC,YAAYhB;IACd;AACF"}