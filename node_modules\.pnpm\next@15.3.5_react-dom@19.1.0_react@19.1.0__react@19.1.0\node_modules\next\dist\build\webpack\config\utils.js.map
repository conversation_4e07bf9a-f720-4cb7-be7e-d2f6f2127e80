{"version": 3, "sources": ["../../../../src/build/webpack/config/utils.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { NextConfigComplete } from '../../../server/config-shared'\n\nexport type ConfigurationContext = {\n  // If the `appDir` feature is enabled\n  hasAppDir: boolean\n  // If the current rule matches a resource in the app layer\n  isAppDir?: boolean\n  supportedBrowsers: string[] | undefined\n  rootDirectory: string\n  customAppFile: RegExp | undefined\n\n  isDevelopment: boolean\n  isProduction: boolean\n\n  isServer: boolean\n  isClient: boolean\n  isEdgeRuntime: boolean\n  targetWeb: boolean\n\n  assetPrefix: string\n\n  sassOptions: any\n  productionBrowserSourceMaps: boolean\n  serverSourceMaps: boolean\n\n  transpilePackages: NextConfigComplete['transpilePackages']\n\n  future: NextConfigComplete['future']\n  experimental: NextConfigComplete['experimental']\n}\n\nexport type ConfigurationFn = (\n  a: webpack.Configuration\n) => webpack.Configuration\n\nexport const pipe =\n  <R>(...fns: Array<(a: R) => R | Promise<R>>) =>\n  (param: R) =>\n    fns.reduce(\n      async (result: R | Promise<R>, next) => next(await result),\n      param\n    )\n"], "names": ["pipe", "fns", "param", "reduce", "result", "next"], "mappings": ";;;;+BAoCaA;;;eAAAA;;;AAAN,MAAMA,OACX,CAAI,GAAGC,MACP,CAACC,QACCD,IAAIE,MAAM,CACR,OAAOC,QAAwBC,OAASA,KAAK,MAAMD,SACnDF"}