{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parse-dynamic-code-evaluation-error.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { formatModuleTrace, getModuleTrace } from './getModuleTrace'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nexport function getDynamicCodeEvaluationError(\n  message: string,\n  module: webpack.NormalModule,\n  compilation: webpack.Compilation,\n  compiler: webpack.Compiler\n): SimpleWebpackError {\n  const { moduleTrace } = getModuleTrace(module, compilation, compiler)\n  const { formattedModuleTrace, lastInternalFileName, invalidImportMessage } =\n    formatModuleTrace(compiler, moduleTrace)\n\n  return new SimpleWebpackError(\n    lastInternalFileName,\n    message +\n      invalidImportMessage +\n      '\\n\\nImport trace for requested module:\\n' +\n      formattedModuleTrace\n  )\n}\n"], "names": ["getDynamicCodeEvaluationError", "message", "module", "compilation", "compiler", "moduleTrace", "getModuleTrace", "formattedModuleTrace", "lastInternalFileName", "invalidImportMessage", "formatModuleTrace", "SimpleWebpackError"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;gCAHkC;oCACf;AAE5B,SAASA,8BACdC,OAAe,EACfC,MAA4B,EAC5BC,WAAgC,EAChCC,QAA0B;IAE1B,MAAM,EAAEC,WAAW,EAAE,GAAGC,IAAAA,8BAAc,EAACJ,QAAQC,aAAaC;IAC5D,MAAM,EAAEG,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE,GACxEC,IAAAA,iCAAiB,EAACN,UAAUC;IAE9B,OAAO,IAAIM,sCAAkB,CAC3BH,sBACAP,UACEQ,uBACA,6CACAF;AAEN"}